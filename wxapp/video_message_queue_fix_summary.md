# 视频咨询消息队列修复总结

## 问题描述
原有的视频咨询消息分发逻辑存在两个关键问题：
1. **消息分发问题**：当用户不在视频咨询页面时，视频咨询MQ消息会被忽略/丢弃，导致用户错过重要消息
2. **consultId匹配问题**：在消息匹配时使用了错误的consultId，应该使用从`api.videoPayInfo`接口返回的`videoConsultId`而不是页面参数中的`consultId`

## 解决方案

### 1. 全局消息队列机制 (app.js)
- 添加 `videoConsultMessageQueues` 全局队列，按 consultId 分组存储消息
- 新增 `addVideoConsultMessage()` 方法将消息添加到队列
- 新增 `getAndClearVideoConsultMessages()` 方法获取并清空队列消息
- 新增 `tryDistributeToVideoConsultRoom()` 方法尝试直接分发消息

### 2. 改进消息分发逻辑 (app.js - distributeMessage)
**原逻辑**：只有当前页面是视频咨询室时才分发消息，否则忽略
**新逻辑**：
1. 优先尝试直接分发给视频咨询室页面（遍历页面栈查找）
2. 如果分发失败，将消息添加到队列中保存
3. 确保消息不会丢失

### 3. 修复consultId匹配逻辑 (pages_videoConsult/consultRoom/index.js)
**问题**：在 `updateResult` 方法中使用 `this.data.consultId`（页面参数）进行消息匹配
**修复**：优先使用 `this.data.videoConsultId`（从 `api.videoPayInfo` 返回），其次使用 `this.data.consultId`

### 4. 视频咨询室页面改进 (pages_videoConsult/consultRoom/index.js)
- 添加 `processQueuedVideoConsultMessages()` 方法处理队列消息
- 在页面初始化和显示时自动处理队列中的消息
- 添加多重防护措施：
  - consultId 多来源获取策略（videoConsultId > consultId > pageOptions）
  - 页面数据初始化状态检查
  - 延迟重试机制
  - 错误处理机制

## 关键修改文件

### app.js
- 第535-627行：全局数据和方法定义
- 第310-363行：消息分发逻辑修改

### pages_videoConsult/consultRoom/index.js  
- 第182行：initPageWithUserInfo 中调用队列处理
- 第205行：onShow 中调用队列处理
- 第246-320行：processQueuedVideoConsultMessages 方法实现
- 第771行：修复 updateResult 中的 consultId 匹配逻辑

## consultId vs videoConsultId 说明

### consultId（页面参数）
- 来源：页面跳转时的URL参数 `options.consultId`
- 用途：页面初始化时的标识
- 特点：可能是临时的或不准确的

### videoConsultId（API返回）
- 来源：调用 `api.videoPayInfo` 接口后返回的真实视频咨询ID
- 用途：后端系统中的真实视频咨询会话标识
- 特点：准确且唯一，用于消息匹配

### 修复策略
```javascript
// 修复前：只使用页面参数
if (Number(message.consultId) !== Number(this.data.consultId))

// 修复后：优先使用API返回的videoConsultId
const currentVideoConsultId = this.data.videoConsultId || this.data.consultId
if (Number(message.consultId) !== Number(currentVideoConsultId))
```

## 工作流程

### 用户在其他页面时收到消息
1. MQTT消息到达 → app.js onMessageArrived
2. 识别为视频咨询消息 → distributeMessage
3. 尝试直接分发 → tryDistributeToVideoConsultRoom
4. 分发失败 → addVideoConsultMessage 加入队列

### 用户进入视频咨询室页面
1. 页面初始化 → initPageWithUserInfo
2. 调用 processQueuedVideoConsultMessages（传入consultId）
3. 获取队列消息 → getAndClearVideoConsultMessages  
4. 逐个处理消息 → onMessageArrived → updateResult（使用正确的videoConsultId匹配）

### 用户在视频咨询室页面时收到消息
1. MQTT消息到达 → distributeMessage
2. 直接分发成功 → onMessageArrived → updateResult（使用正确的videoConsultId匹配）
3. 立即显示

## 特性优势
- ✅ 消息不丢失：无论用户在哪个页面都能正确处理
- ✅ 正确匹配：使用准确的videoConsultId进行消息匹配
- ✅ 会话隔离：不同 consultId 的消息分别存储
- ✅ 内存安全：队列限制50条，获取后立即清空
- ✅ 向后兼容：不影响图文咨询消息分发
- ✅ 错误恢复：包含重试和错误处理机制

## 验证方法
1. 在非视频咨询页面触发消息推送
2. 进入视频咨询室页面
3. 检查消息是否正常显示且consultId匹配正确
4. 查看控制台日志确认队列操作和consultId使用情况
