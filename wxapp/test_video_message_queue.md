# 视频咨询消息队列功能测试说明

## 功能概述

修改了微信小程序中的视频咨询消息分发逻辑，解决了当用户不在视频咨询页面时，视频咨询MQ消息被忽略/丢弃的问题。

## 主要改进

### 1. 全局消息队列机制 (app.js)

在 `app.js` 的 `globalData` 中添加了视频咨询消息队列：

```javascript
// 视频咨询消息队列 - 按consultId分组存储
videoConsultMessageQueues: {}
```

### 2. 新增的全局方法

#### addVideoConsultMessage(message, consultId)
- 将视频咨询消息添加到队列中
- 按 consultId 分组存储
- 限制队列长度为50条，避免内存溢出

#### getAndClearVideoConsultMessages(consultId)
- 获取指定 consultId 的所有队列消息
- 获取后清空该队列
- 返回消息数组

#### tryDistributeToVideoConsultRoom(message, len)
- 尝试将消息直接分发给视频咨询室页面
- 遍历所有页面栈，查找视频咨询室页面
- 返回是否成功分发

### 3. 修改的消息分发逻辑

在 `distributeMessage` 方法中：

**原逻辑**：
- 只有当前页面是视频咨询室时才分发视频咨询消息
- 其他情况下直接忽略消息

**新逻辑**：
- 优先尝试直接分发给视频咨询室页面（无论是否为当前页面）
- 如果分发失败，将消息添加到队列中
- 确保消息不会丢失

### 4. 视频咨询室页面改进

#### processQueuedVideoConsultMessages()
- 在页面显示时处理队列中的消息
- 获取并清空当前 consultId 的消息队列
- 逐个处理队列中的消息

#### 调用时机
- `onLoad` 时：页面首次加载时处理队列消息
- `onShow` 时：页面显示时处理队列消息

## 测试场景

### 场景1：用户在其他页面时收到视频咨询消息
1. 用户在首页或其他页面
2. 后端推送视频咨询消息
3. 消息被添加到队列中
4. 用户进入视频咨询室页面
5. 页面自动处理队列中的消息并显示

### 场景2：用户在视频咨询室页面时收到消息
1. 用户在视频咨询室页面
2. 后端推送视频咨询消息
3. 消息直接分发给当前页面
4. 消息立即显示在聊天界面

### 场景3：多个consultId的消息隔离
1. 用户有多个视频咨询会话
2. 不同会话的消息分别存储在不同队列中
3. 进入特定会话页面时只处理对应的消息

## 验证方法

### 1. 日志验证
查看控制台日志，关键日志包括：
- "视频咨询消息已添加到队列"
- "找到视频咨询室页面，分发消息"
- "处理队列中的视频咨询消息"

### 2. 功能验证
1. 在非视频咨询页面时触发视频咨询消息推送
2. 进入视频咨询室页面
3. 检查消息是否正常显示

### 3. 性能验证
- 检查队列长度限制是否生效
- 验证消息获取后队列是否正确清空

## 注意事项

1. **队列长度限制**：每个 consultId 的队列最多保存50条消息
2. **消息去重**：依赖现有的消息去重机制
3. **内存管理**：获取消息后立即清空队列，避免内存泄漏
4. **兼容性**：保持与现有图文咨询消息分发逻辑的兼容性

## 代码位置

- **app.js**: 第535-627行（全局数据和方法）
- **app.js**: 第310-363行（消息分发逻辑）
- **pages_videoConsult/consultRoom/index.js**: 第184-284行（队列消息处理）
